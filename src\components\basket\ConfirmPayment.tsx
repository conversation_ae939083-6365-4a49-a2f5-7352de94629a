import React, { useState } from "react";
import { CheckSquare, Square } from "react-feather";
import { <PERSON><PERSON>, ModalContent, ModalBody } from "@heroui/react";
import { Button } from "../ui/button";
import { AddtoOrderState, updateOrder } from "../../services/ordersServices";
import { BasketItem } from "../../types/basket";

interface ConfirmPaymentProps {
  selectedItem: BasketItem | null;
  onConfirm: () => void;
  currencySymbol?: string; // Added currency symbol prop
  profileDetails?: {
    email?: any;
    stripe_id?: any;
    id: string;
    avatar?: string;
    profile_name?: string;
  } | null;
  userProfileDetails?: {
    email?: any;
    profile_name?: any;
    id: string;
  } | null;
}

const ConfirmPayment: React.FC<ConfirmPaymentProps> = ({
  selectedItem,
  onConfirm,
  currencySymbol = "$", // Default to $ if not provided
  profileDetails,
  userProfileDetails,
}) => {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [checked, setChecked] = useState(false);
  const [orderPlacedSuccessfully, setOrderPlacedSuccessfully] = useState(false);

  if (!selectedItem) return null;

  console.log("ConfirmPayment received data:", {
    selectedItem,
    profileDetails,
    userProfileDetails,
  });

  const transactionFee = selectedItem.subtotal * 0.04;
  const total = selectedItem.subtotal + transactionFee;

  console.log(selectedItem.orderId);
  console.log("Updating order status to NEW", {
    id: selectedItem?.orderId?.toString() ?? "",
    loggedInUser: userProfileDetails?.profile_name,
    sellerName: profileDetails?.profile_name ?? "",
    userName: userProfileDetails?.profile_name,
  });

  const handlePaymentConfirm = async () => {
    console.log("=== handlePaymentConfirm called ===");

    try {
      // Log all available data
      console.log("Selected Item:", selectedItem);
      console.log("Profile Details:", profileDetails);
      console.log("User Profile Details:", userProfileDetails);
      console.log("Currency Symbol:", currencySymbol);
      console.log("Transaction Fee:", transactionFee);
      console.log("Total:", total);

      // Check if we have the required data
      if (!selectedItem || !profileDetails || !userProfileDetails) {
        console.error("Missing required data:", {
          hasSelectedItem: !!selectedItem,
          hasProfileDetails: !!profileDetails,
          hasUserProfileDetails: !!userProfileDetails,
        });
        return;
      }

      // Prepare escrow payment data
      const escrowData = {
        userId: userProfileDetails.id,
        userEmail: userProfileDetails.email, // You'll need to get this from user data
        sellerId: profileDetails.id,
        sellerEmail: profileDetails.email, // You'll need to get this from seller data
        sellerStripeAccountId: profileDetails.stripe_id, // You'll need to get this from seller data
        orderId: selectedItem.orderId || selectedItem.id.toString(),
        amount: Math.round(total * 100), // Convert to cents
        currency: "usd",
        productName: selectedItem.title,
        productDescription: selectedItem.description || selectedItem.title || "Service order",
      };

      console.log("Escrow payment data prepared:", escrowData);

      // Call the escrow API
      const response = await fetch("/api/escrow/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(escrowData),
      });

      const result = await response.json();
      console.log("Escrow API response:", result);

  await AddtoOrderState({
              id: selectedItem?.orderId?.toString() ?? "",
              loggedInUser: userProfileDetails?.profile_name,
              sellerName: profileDetails?.profile_name ?? "",
              userName: userProfileDetails?.profile_name,
            });
            console.log("Order status updated to NEW");

      if (response.ok && result.success) {
        console.log("✅ Escrow payment created successfully!");
        console.log("Checkout URL:", result.checkoutUrl);
        console.log("Transaction ID:", result.transactionId);
        console.log("Payment Intent ID:", result.paymentIntentId);
        console.log("Session ID:", result.sessionId);

        // Open the checkout URL and only update order status if URL exists
        if (result.checkoutUrl) {
          console.log("Opening checkout URL:", result.checkoutUrl);
          window.location.href = result.checkoutUrl; // Use location.href instead of window.open
          console.log(selectedItem.id);

          // Update order status only when checkoutUrl is available
          console.log("Updating order status to NEW inner", {
            id: selectedItem?.orderId?.toString() ?? "",
            loggedInUser: userProfileDetails?.profile_name,
            sellerName: profileDetails?.profile_name ?? "",
            userName: userProfileDetails?.profile_name,
          });
          if (selectedItem?.orderId) {
            await AddtoOrderState({
              id: selectedItem?.orderId?.toString() ?? "",
              loggedInUser: userProfileDetails?.profile_name,
              sellerName: profileDetails?.profile_name ?? "",
              userName: userProfileDetails?.profile_name,
            });
            console.log("Order status updated to NEW");
          }

          console.log("Updating order status to NEW outer", {
            id: selectedItem?.orderId?.toString() ?? "",
            loggedInUser: userProfileDetails?.profile_name,
            sellerName: profileDetails?.profile_name ?? "",
            userName: userProfileDetails?.profile_name,
          });
          // Set success state to change modal message
          setOrderPlacedSuccessfully(true);
        } else {
          console.error("No checkout URL received from API");
        }
      } else {
        console.error("❌ Escrow payment failed:", result.error || result);
      }
    } catch (error) {
      console.error("❌ Error in handlePaymentConfirm:", error);
    }

    setIsPaymentModalOpen(false);
    onConfirm();
  };

  return (
    <div className="flex flex-col mr-4">
      {/* <div className="flex items-center gap-4 mb-6 border-b pb-4">
        <img src={selectedItem.image} alt="" className="w-12 h-12 rounded-full" />
        <div>
          <p className="text-lg font-semibold">{selectedItem.userName}</p>
          <p className="text-primary font-bold">{selectedItem.title}</p>
        </div>
      </div> */}

      <div className="flex justify-between">
        <p className="text-[#404040]">Subtotal</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {selectedItem.subtotal.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between my-1">
        <p className="text-[#404040]">Transaction fee (4%)</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {transactionFee.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between mb-4">
        <p className="text-[#404040]">Order total</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {total.toFixed(2)}
        </p>
      </div>

      <div className="flex justify-between text-subtitle mb-2">
        <p>Delivery time</p>
        <p className="font-semibold">{selectedItem.time}</p>
      </div>

      <div className="flex flex-row gap-2 mt-2 border-b-2 pb-3">
        <div onClick={() => setChecked((prev) => !prev)} className="cursor-pointer select-none">
          {checked ? <CheckSquare color="#333333" /> : <Square color="#bdbdbd" />}
        </div>
        <div>
          <p className="text-primary">Request an invoice </p>
          <p className="text-[#898887]">
            Note: to obtain an Invoice you'll need to provide your tax details (legal name, address
            and VAT registration number).
          </p>
        </div>
      </div>
      <div className="mt-3">
        <p className="text-subtitle">
          Terms: By placing your order, you confirm that you agree to the User Terms and Conditions.
        </p>
        <button
          onClick={() => setIsPaymentModalOpen(true)}
          className="btn-xs text-white btn py-4 w-full bg-primary rounded-full mt-2"
        >
          Confirm Payment
        </button>
      </div>

      <Modal
        placement="auto"
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        hideCloseButton={true}
      >
        <ModalContent className="modal-content w-80 p-12 rounded-3xl">
          {() => (
            <>
              <ModalBody>
                <p className="text-center text-black text-lg">
                  {orderPlacedSuccessfully
                    ? "Order placed successfully!"
                    : `Confirm payment of ${currencySymbol}${total.toFixed(2)}?`}
                </p>
                <div>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base"
                    onClick={handlePaymentConfirm}
                  >
                    Yes, proceed with payment
                  </Button>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                    onClick={() => setIsPaymentModalOpen(false)}
                  >
                    No, cancel
                  </Button>
                </div>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ConfirmPayment;
